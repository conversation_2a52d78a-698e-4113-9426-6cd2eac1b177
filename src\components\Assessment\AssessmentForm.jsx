import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import AssessmentQuestion from './AssessmentQuestion';
import ProgressBar from '../UI/ProgressBar';

const AssessmentForm = ({ 
  assessmentData, 
  onSubmit, 
  onNext, 
  onPrevious, 
  isLastAssessment = false,
  currentStep = 1,
  totalSteps = 3
}) => {
  const [answers, setAnswers] = useState({});
  const [currentPage, setCurrentPage] = useState(0);
  const questionsPerPage = 5;
  
  // Flatten all questions from all categories
  const allQuestions = [];
  Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
    // Regular questions
    category.questions.forEach((question, index) => {
      allQuestions.push({
        question,
        categoryKey,
        questionKey: `${categoryKey}_${index}`,
        isReverse: false
      });
    });
    
    // Reverse questions (for Big Five)
    if (category.reverseQuestions) {
      category.reverseQuestions.forEach((question, index) => {
        allQuestions.push({
          question,
          categoryKey,
          questionKey: `${categoryKey}_reverse_${index}`,
          isReverse: true
        });
      });
    }
  });

  const totalPages = Math.ceil(allQuestions.length / questionsPerPage);
  const currentQuestions = allQuestions.slice(
    currentPage * questionsPerPage,
    (currentPage + 1) * questionsPerPage
  );

  const handleAnswerChange = (questionKey, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionKey]: value
    }));
  };

  const handleNextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(prev => prev + 1);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const handlePreviousPage = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
      // Scroll to top of the page
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const calculateScores = () => {
    const scores = {};
    
    Object.entries(assessmentData.categories).forEach(([categoryKey, category]) => {
      let totalScore = 0;
      let questionCount = 0;
      
      // Regular questions
      category.questions.forEach((_, index) => {
        const questionKey = `${categoryKey}_${index}`;
        if (answers[questionKey]) {
          totalScore += answers[questionKey];
          questionCount++;
        }
      });
      
      // Reverse questions (for Big Five)
      if (category.reverseQuestions) {
        category.reverseQuestions.forEach((_, index) => {
          const questionKey = `${categoryKey}_reverse_${index}`;
          if (answers[questionKey]) {
            // Reverse the score (6 - original score for 1-5 scale)
            totalScore += (6 - answers[questionKey]);
            questionCount++;
          }
        });
      }
      
      // Calculate average score (0-100 scale)
      if (questionCount > 0) {
        scores[categoryKey] = Math.round((totalScore / questionCount) * 20); // Convert 1-5 to 0-100
      }
    });
    
    return scores;
  };

  const handleSubmit = () => {
    const scores = calculateScores();
    onSubmit(scores);
  };

  const isPageComplete = () => {
    return currentQuestions.every(q => answers[q.questionKey] !== undefined);
  };

  const isAssessmentComplete = () => {
    return allQuestions.every(q => answers[q.questionKey] !== undefined);
  };

  const progress = (Object.keys(answers).length / allQuestions.length) * 100;

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-3xl font-bold text-gray-900">
            {assessmentData.title}
          </h1>
          <div className="text-sm text-gray-500">
            Step {currentStep} of {totalSteps}
          </div>
        </div>
        
        <p className="text-gray-600 mb-4">
          {assessmentData.description}
        </p>
        
        {/* Progress bar */}
        <ProgressBar
          progress={progress}
          label={`Progress: ${Object.keys(answers).length}/${allQuestions.length} questions`}
          showPercentage={true}
        />
      </div>

      {/* Questions */}
      <div className="mb-8">
        <div className="mb-4">
          <h2 className="text-xl font-semibold text-gray-800">
            Page {currentPage + 1} of {totalPages}
          </h2>
        </div>
        
        {currentQuestions.map((q, index) => (
          <AssessmentQuestion
            key={q.questionKey}
            question={q.question}
            questionIndex={currentPage * questionsPerPage + index}
            scale={assessmentData.scale}
            value={answers[q.questionKey]}
            onChange={(value) => handleAnswerChange(q.questionKey, value)}
            isReverse={q.isReverse}
          />
        ))}
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-4">
          {currentPage > 0 && (
            <button
              onClick={handlePreviousPage}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Previous Page
            </button>
          )}
          
          {currentStep > 1 && currentPage === 0 && (
            <button
              onClick={onPrevious}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              Previous Assessment
            </button>
          )}
        </div>

        <div className="flex space-x-4">
          {currentPage < totalPages - 1 && (
            <button
              onClick={handleNextPage}
              disabled={!isPageComplete()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next Page
            </button>
          )}
          
          {currentPage === totalPages - 1 && !isLastAssessment && (
            <button
              onClick={onNext}
              disabled={!isAssessmentComplete()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next Assessment
            </button>
          )}
          
          {currentPage === totalPages - 1 && isLastAssessment && (
            <button
              onClick={handleSubmit}
              disabled={!isAssessmentComplete()}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Submit All Assessments
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AssessmentForm;
