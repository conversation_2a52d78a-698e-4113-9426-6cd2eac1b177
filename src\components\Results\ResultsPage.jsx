import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

const ResultsPage = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchResult = async () => {
      try {
        const response = await axios.get(`/archive/results/${resultId}`);
        
        if (response.data.success) {
          setResult(response.data.data);
        }
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to load results');
      } finally {
        setIsLoading(false);
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderPersonaProfile = (personaProfile) => {
    if (!personaProfile) {
      return <p className="text-gray-600">No persona profile available.</p>;
    }

    const getProspectColor = (level) => {
      const colors = {
        'super high': 'text-green-600 bg-green-100',
        'high': 'text-green-500 bg-green-50',
        'moderate': 'text-yellow-600 bg-yellow-100',
        'low': 'text-orange-600 bg-orange-100',
        'super low': 'text-red-600 bg-red-100'
      };
      return colors[level] || 'text-gray-600 bg-gray-100';
    };

    const formatProspectLabel = (key) => {
      const labels = {
        jobAvailability: 'Job Availability',
        salaryPotential: 'Salary Potential',
        careerProgression: 'Career Progression',
        industryGrowth: 'Industry Growth',
        skillDevelopment: 'Skill Development'
      };
      return labels[key] || key;
    };

    return (
      <div className="space-y-8">
        {/* Archetype & Summary */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-2xl font-bold text-indigo-600 mb-4">
            {personaProfile.archetype}
          </h3>
          <p className="text-gray-700 leading-relaxed">
            {personaProfile.shortSummary}
          </p>
        </div>

        {/* Strengths & Weaknesses */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Strengths */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-green-700 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Strengths
            </h4>
            <ul className="space-y-2">
              {personaProfile.strengths?.map((strength, idx) => (
                <li key={idx} className="text-gray-700 flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  {strength}
                </li>
              ))}
            </ul>
          </div>

          {/* Weaknesses */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-orange-700 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              Areas for Development
            </h4>
            <ul className="space-y-2">
              {personaProfile.weaknesses?.map((weakness, idx) => (
                <li key={idx} className="text-gray-700 flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  {weakness}
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Career Recommendations */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2zm4-3a1 1 0 00-1 1v1h2V4a1 1 0 00-1-1zm-3 4a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
            Career Recommendations
          </h4>
          <div className="space-y-6">
            {personaProfile.careerRecommendation?.map((career, idx) => (
              <div key={idx} className="border border-gray-200 rounded-lg p-4">
                <h5 className="text-lg font-medium text-gray-900 mb-3">
                  {career.careerName}
                </h5>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                  {Object.entries(career.careerProspect || {}).map(([key, value]) => (
                    <div key={key} className="text-center">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${getProspectColor(value)}`}>
                        {value}
                      </div>
                      <div className="text-xs text-gray-600 mt-1">
                        {formatProspectLabel(key)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Insights */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold text-blue-700 mb-4 flex items-center">
            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            Development Insights
          </h4>
          <ul className="space-y-3">
            {personaProfile.insights?.map((insight, idx) => (
              <li key={idx} className="text-gray-700 flex items-start">
                <span className="text-blue-500 mr-2 mt-1">💡</span>
                {insight}
              </li>
            ))}
          </ul>
        </div>

        {/* Work Environment & Role Models */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* Work Environment */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-purple-700 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
              </svg>
              Ideal Work Environment
            </h4>
            <p className="text-gray-700 leading-relaxed">
              {personaProfile.workEnvironment}
            </p>
          </div>

          {/* Role Models */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-indigo-700 mb-4 flex items-center">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
              Inspirational Role Models
            </h4>
            <div className="flex flex-wrap gap-2">
              {personaProfile.roleModel?.map((model, idx) => (
                <span key={idx} className="bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">
                  {model}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderAssessmentData = (assessmentData) => {
    if (!assessmentData) return null;

    return (
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Assessment Scores</h3>
        
        {assessmentData.riasec && (
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-3">RIASEC Holland Codes</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(assessmentData.riasec).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{value}</div>
                  <div className="text-sm text-gray-600 capitalize">{key}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {assessmentData.ocean && (
          <div className="mb-6">
            <h4 className="text-lg font-medium text-gray-800 mb-3">Big Five (OCEAN)</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {Object.entries(assessmentData.ocean).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-2xl font-bold text-green-600">{value}</div>
                  <div className="text-sm text-gray-600 capitalize">{key}</div>
                </div>
              ))}
            </div>
          </div>
        )}
        
        {assessmentData.viaIs && (
          <div>
            <h4 className="text-lg font-medium text-gray-800 mb-3">VIA Character Strengths</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {Object.entries(assessmentData.viaIs).map(([key, value]) => (
                <div key={key} className="text-center">
                  <div className="text-lg font-bold text-purple-600">{value}</div>
                  <div className="text-xs text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600 mx-auto"></div>
          <h2 className="mt-4 text-xl font-semibold text-gray-900">Loading Results...</h2>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="h-16 w-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="h-8 w-8 text-red-600" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </div>
          <h2 className="text-lg font-medium text-red-900 mb-2">Error Loading Results</h2>
          <p className="text-red-700 mb-4">{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <h1 className="text-3xl font-bold text-gray-900">Assessment Results</h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700"
            >
              Back to Dashboard
            </button>
          </div>
          
          {result && (
            <div className="mt-4 text-sm text-gray-600">
              <p>Completed: {formatDate(result.created_at)}</p>
              <p>Status: <span className="capitalize font-medium">{result.status}</span></p>
            </div>
          )}
        </div>

        {result && (
          <div className="space-y-8">
            {/* Persona Profile */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Talent Profile</h2>
              {renderPersonaProfile(result.persona_profile)}
            </div>

            {/* Assessment Data */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Scores</h2>
              {renderAssessmentData(result.assessment_data)}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ResultsPage;
